using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class EntryExitHistoryManager : IEntryExitHistoryService
    {
        IEntryExitHistoryDal _entryExitHistoryDal;


        //buradaki hi� bir �eye securedoperation aspecti ayarlama qr kod okununca buralara girmesi gerekiyor ve yetkisiz eri�im olmal�
        public EntryExitHistoryManager(IEntryExitHistoryDal entryExitHistoryDal)
        {
            _entryExitHistoryDal = entryExitHistoryDal;
        }
        [PerformanceAspect(3)]
        public IResult Add(EntryExitHistory entryExitHistory)
        {
            _entryExitHistoryDal.Add(entryExitHistory);
            return new SuccessResult(Messages.EntryHistoryAdded);
        }

        // QR kod taramas� i�in �zel metot - CompanyID'yi parametre olarak al�r
        [PerformanceAspect(3)]
        public IResult AddWithCompanyId(EntryExitHistory entryExitHistory, int companyId)
        {
            return _entryExitHistoryDal.AddWithCompanyId(entryExitHistory, companyId);
        }
        [PerformanceAspect(3)]
        public IResult Delete(int id)
        {
            _entryExitHistoryDal.Delete(id);
            return new SuccessResult(Messages.EntryExitHistoryDeleted);
        }
        [PerformanceAspect(3)]
        public IDataResult<List<EntryExitHistory>> GetAll()
        {
            return new SuccessDataResult<List<EntryExitHistory>>(_entryExitHistoryDal.GetAll());
        }
        [PerformanceAspect(3)]
        public IResult Update(EntryExitHistory entryExitHistory)
        {
            _entryExitHistoryDal.Update(entryExitHistory);
            return new SuccessResult(Messages.EntryExitHistoryUpdated);
        }
        
        // QR kod taramas� i�in �zel g�ncelleme metodu - CompanyID'yi parametre olarak al�r
        [PerformanceAspect(3)]
        public IResult UpdateWithCompanyId(EntryExitHistory entryExitHistory, int companyId)
        {
            return _entryExitHistoryDal.UpdateWithCompanyId(entryExitHistory, companyId);
        }
       
       
    }
}
