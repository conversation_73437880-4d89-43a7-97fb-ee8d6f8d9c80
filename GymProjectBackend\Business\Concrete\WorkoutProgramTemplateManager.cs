using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;

using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.DTOs;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class WorkoutProgramTemplateManager : IWorkoutProgramTemplateService
    {
        private readonly IWorkoutProgramTemplateDal _workoutProgramTemplateDal;
        private readonly ICompanyContext _companyContext;

        public WorkoutProgramTemplateManager(
            IWorkoutProgramTemplateDal workoutProgramTemplateDal,
            ICompanyContext companyContext)
        {
            _workoutProgramTemplateDal = workoutProgramTemplateDal;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]

        [PerformanceAspect(3)]
        public IDataResult<List<WorkoutProgramTemplateListDto>> GetAll()
        {
            var result = _workoutProgramTemplateDal.GetWorkoutProgramTemplateList();
            return new SuccessDataResult<List<WorkoutProgramTemplateListDto>>(result, Messages.WorkoutProgramsListed);
        }

        [SecuredOperation("owner,admin")]

        [PerformanceAspect(3)]
        public IDataResult<WorkoutProgramTemplateDto> GetById(int templateId)
        {
            // SOLID prensiplerine uygun: Validation logic'i DAL katmanına taşıdık
            var templateResult = _workoutProgramTemplateDal.GetWorkoutProgramTemplateByIdWithValidation(templateId);
            if (!templateResult.Success)
            {
                return new ErrorDataResult<WorkoutProgramTemplateDto>(templateResult.Message);
            }

            var result = _workoutProgramTemplateDal.GetWorkoutProgramTemplateDetail(templateId);
            if (result == null)
            {
                return new ErrorDataResult<WorkoutProgramTemplateDto>(Messages.WorkoutProgramNotFound);
            }
            return new SuccessDataResult<WorkoutProgramTemplateDto>(result, Messages.WorkoutProgramDetailRetrieved);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(WorkoutProgramTemplateAddValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect]

        public IResult Add(WorkoutProgramTemplateAddDto templateAddDto)
        {
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Business rules validation DAL katmanına taşındı
            return _workoutProgramTemplateDal.AddWorkoutProgramWithBusinessRules(templateAddDto, companyId);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(WorkoutProgramTemplateUpdateValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect]

        public IResult Update(WorkoutProgramTemplateUpdateDto templateUpdateDto)
        {
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Business rules validation DAL katmanına taşındı
            return _workoutProgramTemplateDal.UpdateWorkoutProgramWithBusinessRules(templateUpdateDto, companyId);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]

        [PerformanceAspect(3)]
        public IResult Delete(int templateId)
        {
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Validation logic DAL katmanına taşındı
            return _workoutProgramTemplateDal.SoftDeleteWorkoutProgramWithValidation(templateId, companyId);
        }
    }
}
